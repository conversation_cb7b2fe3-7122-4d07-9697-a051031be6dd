using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using Shared.Infrastructure.Localization;

namespace Shared.Application.Validation;

public static class StringExtensions
{
    public static bool IsValidEmail(this string? email)
    {
        if (string.IsNullOrWhiteSpace(email))
        {
            return false;
        }
        var emailRegex = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
        return Regex.IsMatch(email, emailRegex, RegexOptions.NonBacktracking);
    }

    public static bool IsValidPhone(string? phone)
    {
        return !string.IsNullOrWhiteSpace(phone) &&
            Regex.IsMatch(phone, @"^\+?[0-9]{7,15}$", RegexOptions.NonBacktracking);
    }

    public static bool IsValidTaxNumber(string? taxNumber)
    {
        return !string.IsNullOrWhiteSpace(taxNumber) && taxNumber.All(char.IsDigit);
    }

    public static bool IsValidPostalCode(string? postalCode)
    {
        return !string.IsNullOrWhiteSpace(postalCode) &&
            Regex.IsMatch(postalCode, @"^\d{5}(-\d{4})?$", RegexOptions.NonBacktracking);
    }

    public static bool IsValidDate(string? date)
    {
        return DateTime.TryParse(date, out _);
    }

    public static bool IsValidUrl(string? url)
    {
        return !string.IsNullOrWhiteSpace(url) &&
            Uri.TryCreate(url, UriKind.Absolute, out _);
    }

    public static bool IsValidGuid(string? guid)
    {
        return Guid.TryParse(guid, out _);
    }

    public static bool IsValidEnum<T>(string? value) where T : struct, Enum
    {
        return !string.IsNullOrWhiteSpace(value) &&
            Enum.TryParse(typeof(T), value, true, out _);
    }

    public static string ToNormalizedTitle(this string input)
    {
        return string.IsNullOrWhiteSpace(input)
            ? string.Empty
            : CultureInfo.CurrentCulture.TextInfo.ToTitleCase(input.Trim().ToLower());
    }

    public static string ToNormalizedEmailPart(this string? input, ILocalizer localizer)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return localizer.Get("EmailPart.EmptyFallback");
        }
        string normalized = input.Trim().ToLowerInvariant()
            .Normalize(NormalizationForm.FormD);
        var sb = new StringBuilder();
        foreach (var c in normalized)
        {
            if (CharUnicodeInfo.GetUnicodeCategory(c) != UnicodeCategory.NonSpacingMark)
            {
                sb.Append(c);
            }
        }
        normalized = sb.ToString().Normalize(NormalizationForm.FormC);
        normalized = Regex.Replace(normalized, @"[^a-z0-9\.]", "", RegexOptions.NonBacktracking);
        normalized = Regex.Replace(normalized, @"\.{2,}", ".", RegexOptions.NonBacktracking);
        return normalized.Trim('.');
    }
}
