using Conversations.Application.Abstractions;
using Conversations.Domain.AutoDialers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Conversations.Infrastructure.BackgroundServices;

public class AutoDialerAutoStartService(
    IServiceScopeFactory scopeFactory,
    ILogger<AutoDialerAutoStartService> logger
) : BackgroundService
{
    private readonly IServiceScopeFactory _scopeFactory = scopeFactory;

    private readonly ILogger<AutoDialerAutoStartService> _logger = logger;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessExpiredAutoDialers(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while processing expired auto dialers");
            }

            await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
        }
    }

    private async Task ProcessExpiredAutoDialers(CancellationToken cancellationToken)
    {
        using var scope = _scopeFactory.CreateScope();
        var _dbContext = scope.ServiceProvider.GetRequiredService<IConversationDbContext>();
        var _threeCXService = scope.ServiceProvider.GetRequiredService<IThreeCXService>();

        var expiredAutoDialers = await _dbContext.AutoDialers
            .Include(x => x.AutoDialerNumbers)
            .Where(p => p.Status == AutoDialerStatus.Pending)
            .Where(p => p.StartDate <= DateTime.Now)
            .ToListAsync(cancellationToken);

        foreach (var autoDialer in expiredAutoDialers)
        {
            try
            {
                var targetNumbers = autoDialer.AutoDialerNumbers.OrderBy(x => x.Order).Select(x => x.PhoneNumber).ToList();
                var result = await _threeCXService.StartAutoDialer(autoDialer.Id, autoDialer.QueueNumber, targetNumbers, autoDialer.RetryCount, autoDialer.CallDelayAfterSuccess);
                if (result.IsSuccess)
                {
                    autoDialer.Start();
                    await _dbContext.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation(
                        "Autoailer started automatically {AutoailerId} ",
                        autoDialer.Id);
                }
                else
                {
                    _logger.LogError(result.Error.Code + " " + result.Error.Description);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,
                    "Failed to auto-start autodialer {AutoailerId}",
                    autoDialer.Id);
            }
        }
    }


}
