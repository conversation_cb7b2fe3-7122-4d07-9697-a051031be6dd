using Conversations.Domain.AutoDialers;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Conversations.Infrastructure.Data.Configurations;

public class AutoDialerConfiguration : IEntityTypeConfiguration<AutoDialer>
{
    public void Configure(EntityTypeBuilder<AutoDialer> builder)
    {
        builder.ToTable("AutoDialer", "Conversations");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.QueueNumber)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(e => e.StartDate)
            .IsRequired();

        builder.Property(e => e.Active)
            .IsRequired();

        builder.Property(e => e.Status)
            .IsRequired();

        builder.Property(e => e.RetryCount)
            .IsRequired()
            .HasDefaultValue(3);

        builder.Property(e => e.CallDelayAfterSuccess)
            .IsRequired()
            .HasDefaultValue(10);

        // AutoDialerNumbers ile ilişki
        builder.HasMany(e => e.AutoDialerNumbers)
            .WithOne(n => n.AutoDialer)
            .HasForeignKey(n => n.AutoDialerId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
