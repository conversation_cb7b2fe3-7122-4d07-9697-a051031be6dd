using Conversations.Domain.AutoDialers;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Conversations.Infrastructure.Data.Configurations;

public class AutoDialerNumberConfiguration : IEntityTypeConfiguration<AutoDialerNumber>
{
    public void Configure(EntityTypeBuilder<AutoDialerNumber> builder)
    {
        builder.ToTable("AutoDialerNumber", "Conversations");

        builder.<PERSON><PERSON><PERSON>(e => e.Id);

        builder.Property(e => e.PhoneNumber)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(e => e.Order)
            .IsRequired();

        builder.Property(e => e.AutoDialerId)
            .IsRequired();

        // AutoDialer ile ilişki
        builder.HasOne(e => e.AutoDialer)
            .WithMany(a => a.AutoDialerNumbers)
            .HasForeignKey(e => e.AutoDialerId)
            .OnDelete(DeleteBehavior.Cascade);

        // Index
        builder.HasIndex(e => new { e.AutoDialerId, e.Order })
            .IsUnique();
    }
}
