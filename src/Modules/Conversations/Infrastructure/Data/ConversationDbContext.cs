using System.Reflection;
using Conversations.Application.Abstractions;
using Conversations.Domain.AutoDialers;
using Conversations.Domain.BlackLists;
using Conversations.Domain.Calls;
using Conversations.Domain.Chats;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Infrastructure.Data;

namespace Conversations.Infrastructure.Data;

public class ConversationDbContext(
    DbContextOptions<ConversationDbContext> options,
    IWorkContext workContext,
    IEventBus eventBus
) : BaseDbContext(options, workContext, eventBus), IConversationDbContext
{
    public DbSet<Call> Call { get; set; }
    public DbSet<CallNote> CallNote { get; set; }
    public DbSet<Chat> Chat { get; set; }
    public DbSet<ChatMessage> ChatMessage { get; set; }
    public DbSet<ChatAttachment> ChatAttachment { get; set; }
    public DbSet<AutoDialer> AutoDialers { get; set; }
    public DbSet<AutoDialerNumber> AutoDialerNumbers { get; set; }
    public DbSet<BlackList> BlackLists { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        modelBuilder.HasDefaultSchema("Conversations");
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }
}
