﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Conversations.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class ConversationData016 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TargetNumbers",
                schema: "Conversations",
                table: "AutoDialer");

            migrationBuilder.AddColumn<int>(
                name: "CallDelayAfterSuccess",
                schema: "Conversations",
                table: "AutoDialer",
                type: "int",
                nullable: false,
                defaultValue: 10);

            migrationBuilder.AddColumn<int>(
                name: "RetryCount",
                schema: "Conversations",
                table: "AutoDialer",
                type: "int",
                nullable: false,
                defaultValue: 3);

            migrationBuilder.CreateTable(
                name: "AutoDialerNumber",
                schema: "Conversations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AutoDialerId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PhoneNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Order = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AutoDialerNumber", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AutoDialerNumber_AutoDialer_AutoDialerId",
                        column: x => x.AutoDialerId,
                        principalSchema: "Conversations",
                        principalTable: "AutoDialer",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AutoDialerNumber_AutoDialerId_Order",
                schema: "Conversations",
                table: "AutoDialerNumber",
                columns: new[] { "AutoDialerId", "Order" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AutoDialerNumber",
                schema: "Conversations");

            migrationBuilder.DropColumn(
                name: "CallDelayAfterSuccess",
                schema: "Conversations",
                table: "AutoDialer");

            migrationBuilder.DropColumn(
                name: "RetryCount",
                schema: "Conversations",
                table: "AutoDialer");

            migrationBuilder.AddColumn<string>(
                name: "TargetNumbers",
                schema: "Conversations",
                table: "AutoDialer",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }
    }
}
