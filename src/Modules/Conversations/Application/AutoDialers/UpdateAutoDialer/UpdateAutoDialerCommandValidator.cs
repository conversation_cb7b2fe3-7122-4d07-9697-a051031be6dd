using FluentValidation;

namespace Conversations.Application.AutoDialers.UpdateAutoDialer;

public class UpdateAutoDialerCommandValidator : AbstractValidator<UpdateAutoDialerCommand>
{
    public UpdateAutoDialerCommandValidator()
    {
        RuleFor(v => v.Id)
            .NotEmpty().WithMessage("ID is required.");

        RuleFor(v => v.Name)
            .NotEmpty().WithMessage("Name is required.")
            .MaximumLength(100).WithMessage("Name must not exceed 100 characters.");

        RuleFor(v => v.QueueNumber)
            .NotEmpty().WithMessage("Queue Number is required.")
            .MaximumLength(20).WithMessage("Queue Number must not exceed 20 characters.");

        RuleFor(v => v.StartDate)
            .NotEmpty().WithMessage("Start Date is required.")
            .GreaterThanOrEqualTo(DateTime.Today).WithMessage("Start Date must be today or in the future.");

        RuleFor(v => v.TargetNumbers)
            .NotEmpty().WithMessage("Target Numbers is required.")
            .Must(x => x != null && x.Count > 0).WithMessage("At least one target number is required.");

        RuleFor(v => v.RetryCount)
            .GreaterThanOrEqualTo(0).WithMessage("Retry Count must be greater than or equal to 0.")
            .LessThanOrEqualTo(10).WithMessage("Retry Count must be less than or equal to 10.");

        RuleFor(v => v.CallDelayAfterSuccess)
            .GreaterThanOrEqualTo(0).WithMessage("Call Delay After Success must be greater than or equal to 0.")
            .LessThanOrEqualTo(300).WithMessage("Call Delay After Success must be less than or equal to 300 seconds.");
    }
}
