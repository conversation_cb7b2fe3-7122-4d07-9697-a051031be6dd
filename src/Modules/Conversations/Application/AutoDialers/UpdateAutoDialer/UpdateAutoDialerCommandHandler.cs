using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.AutoDialers.UpdateAutoDialer;

public class UpdateAutoDialerCommandHandler(
    IConversationDbContext dbContext
) : IRequestHandler<UpdateAutoDialerCommand, Result<bool>>
{
    private readonly IConversationDbContext _dbContext = dbContext;

    public async Task<Result<bool>> Handle(UpdateAutoDialerCommand request, CancellationToken cancellationToken)
    {
        var entity = await _dbContext.AutoDialers
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (entity == null)
        {
            return Result.Failure<bool>("404", "AutoDialer not found.");
        }
        entity.Update(
            request.Name,
            request.Active,
            request.QueueNumber,
            request.StartDate,
            request.TargetNumbers,
            request.RetryCount,
            request.CallDelayAfterSuccess);
        await _dbContext.SaveChangesAsync(cancellationToken);
        return Result.Success(true);
    }
}
