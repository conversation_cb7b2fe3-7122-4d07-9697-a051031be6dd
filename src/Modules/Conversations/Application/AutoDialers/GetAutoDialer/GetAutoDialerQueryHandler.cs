using Conversations.Application.Abstractions;
using Conversations.Domain.Calls;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.AutoDialers.GetAutoDialer;

public class GetAutoDialerQueryHandler(
    IConversationDbContext dbContext
) : IRequestHandler<GetAutoDialerQuery, Result<AutoDialerDto>>
{
    public async Task<Result<AutoDialerDto>> <PERSON><PERSON>(GetAutoDialerQuery request, CancellationToken cancellationToken)
    {
        var entity = await dbContext.AutoDialers
            .Include(x => x.AutoDialerNumbers)
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
        if (entity == null)
        {
            return Result.Failure<AutoDialerDto>("AutoDialer not found.");
        }
        var dto = entity.ToDto();
        dto.TotalCount = entity.AutoDialerNumbers.Count;
        dto.DoneCount = await dbContext.Call.CountAsync(c => c.AutoDialerId == entity.Id, cancellationToken: cancellationToken);
        dto.EndedCount = await dbContext.Call.CountAsync(c => c.AutoDialerId == entity.Id && c.Status == CallStatus.Ended, cancellationToken: cancellationToken);
        dto.MissedCaount = await dbContext.Call.CountAsync(c => c.AutoDialerId == entity.Id && c.Status == CallStatus.Missed, cancellationToken: cancellationToken);
        return Result.Success(dto);
    }
}
