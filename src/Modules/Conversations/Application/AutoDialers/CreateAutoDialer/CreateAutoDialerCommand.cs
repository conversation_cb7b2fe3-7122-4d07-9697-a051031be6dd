using MediatR;
using Shared.Application;

namespace Conversations.Application.AutoDialers.CreateAutoDialer;

public record CreateAutoDialerCommand : IRequest<Result<Guid>>
{
    public string Name { get; init; }
    public bool Active { get; init; }
    public string QueueNumber { get; init; }
    public DateTime StartDate { get; init; }
    public List<string> TargetNumbers { get; init; }
    public int RetryCount { get; init; } = 3;
    public int CallDelayAfterSuccess { get; init; } = 10;
}
