using Conversations.Application.Abstractions;
using Conversations.Domain.AutoDialers;
using MediatR;
using Shared.Application;

namespace Conversations.Application.AutoDialers.CreateAutoDialer;

public class CreateAutoDialerCommandHandler : IRequestHandler<CreateAutoDialerCommand, Result<Guid>>
{
    private readonly IConversationDbContext _dbContext;

    public CreateAutoDialerCommandHandler(IConversationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<Guid>> Handle(CreateAutoDialerCommand request, CancellationToken cancellationToken)
    {
        var entity = AutoDialer.Create(
            request.Name,
            request.Active,
            request.QueueNumber,
            request.StartDate,
            request.TargetNumbers,
            request.RetryCount,
            request.CallDelayAfterSuccess);

        _dbContext.AutoDialers.Add(entity);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success(entity.Id);
    }
}
