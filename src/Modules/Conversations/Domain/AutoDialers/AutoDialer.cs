using Conversations.Domain.Calls;
using Shared.Domain;

namespace Conversations.Domain.AutoDialers;

public class AutoDialer : AuditableEntity
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public bool Active { get; private set; }
    public string QueueNumber { get; private set; }
    public AutoDialerStatus Status { get; private set; }
    public DateTime StartDate { get; private set; }
    public int RetryCount { get; private set; }
    public int CallDelayAfterSuccess { get; private set; }
    public List<Call> Call { get; set; }
    public List<AutoDialerNumber> AutoDialerNumbers { get; set; } = new();
    public bool IsArchive { get; set; }

    private AutoDialer() { }

    public static AutoDialer Create(
        string name,
        bool active,
        string queueNumber,
        DateTime startDate,
        List<string> targetNumbers)
    {
        var autoDialer = new AutoDialer
        {
            Id = Guid.NewGuid(),
            Name = name,
            Active = active,
            QueueNumber = queueNumber,
            Status = AutoDialerStatus.Pending,
            StartDate = startDate,
            TargetNumbers = targetNumbers,
            InsertDate = DateTime.Now
        };

        autoDialer.Raise(new AutoDialerCreatedEvent(autoDialer.Id));
        return autoDialer;
    }

    public void Update(
        string name,
        bool active,
        string queueNumber,
        DateTime startDate,
        List<string> targetNumbers)
    {
        Name = name;
        Active = active;
        QueueNumber = queueNumber;
        StartDate = startDate;
        TargetNumbers = targetNumbers;
        UpdateDate = DateTime.Now;

        Raise(new AutoDialerUpdatedEvent(Id));
    }

    public void SetStatus(AutoDialerStatus status)
    {
        Status = status;
        UpdateDate = DateTime.Now;

        Raise(new AutoDialerStatusChangedEvent(Id, status));
    }

    public void Start()
    {
        if (Status != AutoDialerStatus.Pending)
        {
            throw new InvalidOperationException("Only pending auto dialers can be started");
        }

        Status = AutoDialerStatus.InProgress;
        UpdateDate = DateTime.Now;

        Raise(new AutoDialerStartedEvent(Id));
    }

    public void Complete()
    {
        if (Status != AutoDialerStatus.InProgress)
        {
            throw new InvalidOperationException("Only in-progress auto dialers can be completed");
        }

        Status = AutoDialerStatus.Completed;
        UpdateDate = DateTime.Now;

        Raise(new AutoDialerCompletedEvent(Id));
    }

    public void Cancel()
    {
        if (Status == AutoDialerStatus.Completed || Status == AutoDialerStatus.Cancelled)
        {
            throw new InvalidOperationException("Completed or already cancelled auto dialers cannot be cancelled");
        }

        Status = AutoDialerStatus.Cancelled;
        UpdateDate = DateTime.Now;

        Raise(new AutoDialerCancelledEvent(Id));
    }

    public void Archive()
    {
        if (Status == AutoDialerStatus.Completed || Status == AutoDialerStatus.Cancelled)
        {
            IsArchive = true;
            UpdateDate = DateTime.Now;
        }
        else
        {
            throw new InvalidOperationException("Sadece tamamlanan veya iptal edilen otomatik arama kayıtları arşivlenebilir.");
        }
    }
}
