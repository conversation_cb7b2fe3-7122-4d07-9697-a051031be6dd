using Shared.Domain;

namespace Conversations.Domain.AutoDialers;

public class AutoDialerNumber : BaseEntity
{
    public Guid Id { get; private set; }
    public Guid AutoDialerId { get; private set; }
    public string PhoneNumber { get; private set; }
    public int Order { get; private set; }
    
    // Navigation property
    public AutoDialer AutoDialer { get; set; }

    private AutoDialerNumber() { }

    public static AutoDialerNumber Create(
        Guid autoDialerId,
        string phoneNumber,
        int order)
    {
        return new AutoDialerNumber
        {
            Id = Guid.NewGuid(),
            AutoDialerId = autoDialerId,
            PhoneNumber = phoneNumber,
            Order = order
        };
    }

    public void Update(string phoneNumber, int order)
    {
        PhoneNumber = phoneNumber;
        Order = order;
    }
}
