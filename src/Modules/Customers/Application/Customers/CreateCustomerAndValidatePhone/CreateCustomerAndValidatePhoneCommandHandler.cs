using System.Security.Cryptography;
using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PhoneNumbers;
using Shared.Application;
using Shared.Domain;
using Shared.Infrastructure.External.Sms;

namespace Customers.Application.Customers.CreateCustomerAndValidatePhone;

internal sealed class CreateCustomerAndValidatePhoneCommandHandler(
    ICustomersDbContext context,
    ISmsManager smsManager,
    IOptionsSnapshot<AppSettings> appSettings
) : IRequestHandler<CreateCustomerAndValidatePhoneCommand, Result<CreateCustomerAndValidatePhoneResponse>>
{
    public async Task<Result<CreateCustomerAndValidatePhoneResponse>> Handle(
        CreateCustomerAndValidatePhoneCommand request,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.Phone))
        {
            return Result.Success(new CreateCustomerAndValidatePhoneResponse(
                Success: false,
                Message: "Telefon numarası boş olamaz.",
                CustomerId: null,
                IsNewCustomer: null
            ));
        }
        var phoneUtil = PhoneNumberUtil.GetInstance();
        var finalNormalized = "";
        var finalPrefix = "";
        try
        {
            var parsedNumber = phoneUtil.Parse(request.Phone, appSettings.Value.DefaultRegion);
            finalNormalized = parsedNumber.NationalNumber.ToString();
            finalPrefix = parsedNumber.CountryCode.ToString();
        }
        catch (Exception)
        {
            return Result.Success(new CreateCustomerAndValidatePhoneResponse(
                Success: false,
                Message: "Geçersiz telefon numarası formatı.",
                CustomerId: null,
                IsNewCustomer: null
            ));
        }
        var existingCustomer = await context.Customers
            .FirstOrDefaultAsync(c => c.Phone == finalNormalized && c.PhonePrefix == finalPrefix, cancellationToken);

        Customer customer;
        bool? IsNewCustomer;
        if (existingCustomer != null)
        {
            customer = existingCustomer;
            IsNewCustomer = !string.IsNullOrWhiteSpace(customer.PhoneVerificationCode);
        }
        else
        {
            customer = Customer.Create(
                finalNormalized,
                finalNormalized,
                finalNormalized + "@generated.local",
                finalNormalized,
                finalPrefix,
                CustomerType.Individual);

            context.Customers.Add(customer);
            IsNewCustomer = true;
        }
        var verificationCode = GenerateVerificationCode();
        customer.SetPhoneVerificationCode(verificationCode);

        try
        {
            var fullPhone = $"{finalPrefix}{finalNormalized}";
            var message = $"Doğrulama kodunuz: {verificationCode}. Bu kod 5 dakika geçerlidir.";
            await smsManager.SendSmsAsync(fullPhone, message);
            await context.SaveChangesAsync(cancellationToken);
            return Result.Success(new CreateCustomerAndValidatePhoneResponse(
                Success: true,
                Message: "Doğrulama kodu gönderildi.",
                CustomerId: customer.Id,
                IsNewCustomer: IsNewCustomer
            ));
        }
        catch (Exception)
        {
            return Result.Success(new CreateCustomerAndValidatePhoneResponse(
                Success: false,
                Message: "SMS gönderilirken hata oluştu.",
                CustomerId: null,
                IsNewCustomer: null
            ));
        }
    }

    private static string GenerateVerificationCode()
    {
        var randomGenerator = RandomNumberGenerator.Create();
        byte[] data = new byte[16];
        randomGenerator.GetBytes(data);
        return BitConverter.ToString(data);
    }
}
