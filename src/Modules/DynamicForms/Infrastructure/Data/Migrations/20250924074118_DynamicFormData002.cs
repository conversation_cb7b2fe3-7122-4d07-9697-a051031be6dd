﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DynamicForms.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class DynamicFormData002 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Default",
                schema: "DynamicForms",
                table: "FormAttribute",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "Disable",
                schema: "DynamicForms",
                table: "FormAttribute",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "FormSectionId",
                schema: "DynamicForms",
                table: "FormAttribute",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<int>(
                name: "Order",
                schema: "DynamicForms",
                table: "FormAttribute",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Size",
                schema: "DynamicForms",
                table: "FormAttribute",
                type: "int",
                nullable: false,
                defaultValue: 12);

            migrationBuilder.AddColumn<string>(
                name: "Key",
                schema: "DynamicForms",
                table: "Form",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "FormSection",
                schema: "DynamicForms",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FormId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Title = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Order = table.Column<int>(type: "int", nullable: false),
                    InsertDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    InsertUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    UpdateUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    History = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FormSection", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FormSection_Form_FormId",
                        column: x => x.FormId,
                        principalSchema: "DynamicForms",
                        principalTable: "Form",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_FormAttribute_FormSectionId",
                schema: "DynamicForms",
                table: "FormAttribute",
                column: "FormSectionId");

            migrationBuilder.CreateIndex(
                name: "IX_Form_Key",
                schema: "DynamicForms",
                table: "Form",
                column: "Key",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FormSection_FormId",
                schema: "DynamicForms",
                table: "FormSection",
                column: "FormId");

            migrationBuilder.AddForeignKey(
                name: "FK_FormAttribute_FormSection_FormSectionId",
                schema: "DynamicForms",
                table: "FormAttribute",
                column: "FormSectionId",
                principalSchema: "DynamicForms",
                principalTable: "FormSection",
                principalColumn: "Id",
                onDelete: ReferentialAction.NoAction);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FormAttribute_FormSection_FormSectionId",
                schema: "DynamicForms",
                table: "FormAttribute");

            migrationBuilder.DropTable(
                name: "FormSection",
                schema: "DynamicForms");

            migrationBuilder.DropIndex(
                name: "IX_FormAttribute_FormSectionId",
                schema: "DynamicForms",
                table: "FormAttribute");

            migrationBuilder.DropIndex(
                name: "IX_Form_Key",
                schema: "DynamicForms",
                table: "Form");

            migrationBuilder.DropColumn(
                name: "Default",
                schema: "DynamicForms",
                table: "FormAttribute");

            migrationBuilder.DropColumn(
                name: "Disable",
                schema: "DynamicForms",
                table: "FormAttribute");

            migrationBuilder.DropColumn(
                name: "FormSectionId",
                schema: "DynamicForms",
                table: "FormAttribute");

            migrationBuilder.DropColumn(
                name: "Order",
                schema: "DynamicForms",
                table: "FormAttribute");

            migrationBuilder.DropColumn(
                name: "Size",
                schema: "DynamicForms",
                table: "FormAttribute");

            migrationBuilder.DropColumn(
                name: "Key",
                schema: "DynamicForms",
                table: "Form");
        }
    }
}
