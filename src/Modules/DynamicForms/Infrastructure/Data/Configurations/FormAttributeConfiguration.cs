using System.Text.Json;
using DynamicForms.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DynamicForms.Infrastructure.Data.Configurations;

public class FormAttributeConfiguration : IEntityTypeConfiguration<FormAttribute>
{
    public void Configure(EntityTypeBuilder<FormAttribute> builder)
    {
        builder.ToTable("FormAttribute");
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Name).IsRequired().HasMaxLength(100);
        builder.Property(x => x.Title).IsRequired().HasMaxLength(100);
        builder.Property(x => x.Data);
        builder.Property(x => x.Size).IsRequired().HasDefaultValue(12);
        builder.Property(x => x.Disable).IsRequired().HasDefaultValue(false);
        builder.Property(x => x.Order).IsRequired().HasDefaultValue(0);
        builder.Property(x => x.Default).HasMaxLength(500);

        builder.HasOne(x => x.Type).WithMany().HasForeignKey(x => x.TypeId).OnDelete(DeleteBehavior.Restrict);
        builder.HasOne(x => x.FormSection).WithMany(x => x.FormAttributes).HasForeignKey(x => x.FormSectionId).OnDelete(DeleteBehavior.NoAction);

        builder.Property(e => e.Validators).HasColumnType("nvarchar(max)").HasConversion(
            v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
            v => JsonSerializer.Deserialize<Dictionary<string, string>>(v, (JsonSerializerOptions)null)
        );
    }
}
