{"scripts": {"watch": "cd src/Host && dotnet watch run", "run": "cd src/Host && dotnet run", "build": "cd src/Host && dotnet build", "build-client": "cd src/Client && npm run build", "clean": "find . -name 'bin' -o -name 'obj' -o -name 'node_modules' -type d -exec rm -rf {} +", "db-update": "npm run build && npm run db-update-users && npm run db-update-customers && npm run db-update-conversations && npm run db-update-dynamicforms && npm run db-update-requests && npm run db-update-general && npm run db-update-calendar", "db-update-users": "cd src/Modules/Users && dotnet ef database update --startup-project ../../Host/Host.csproj --context UserDbContext --no-build", "db-update-customers": "cd src/Modules/Customers && dotnet ef database update --startup-project ../../Host/Host.csproj --context CustomersDbContext --no-build", "db-update-conversations": "cd src/Modules/Conversations && dotnet ef database update --startup-project ../../Host/Host.csproj --context ConversationDbContext --no-build", "db-update-dynamicforms": "cd src/Modules/DynamicForms && dotnet ef database update --startup-project ../../Host/Host.csproj --context DynamicFormDbContext --no-build", "db-update-requests": "cd src/Modules/Requests && dotnet ef database update --startup-project ../../Host/Host.csproj --context RequestsDbContext --no-build", "db-update-tasks": "cd src/Modules/Tasks && dotnet ef database update --startup-project ../../Host/Host.csproj --context TaskDbContext --no-build", "db-update-general": "cd src/Modules/General && dotnet ef database update --startup-project ../../Host/Host.csproj --context GeneralDbContext --no-build", "db-update-calendar": "cd src/Modules/Calendar && dotnet ef database update --startup-project ../../Host/Host.csproj --context CalendarDbContext --no-build", "db-migrations-add": "cd src/Modules/ModuleName && dotnet ef migrations add ModuleNameData001 --startup-project ../../Host/Host.csproj --context ModuleNameDbContext --output-dir Infrastructure/Data/Migrations", "db-migrations-remove": "cd src/Modules/ModuleName && dotnet ef migrations remove --startup-project ../../Host/Host.csproj --context ModuleNameDbContext", "publish-mac": "cd src/Host && dotnet publish Host.csproj -c Release /p:DebugType=None -o ~/Desktop/Publish-CRM"}}